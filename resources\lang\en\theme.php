<?php

return [
    'nav' => [
        'about_us'         => 'About Us',
        'account'        => 'Account',
        'benefits'         => 'benefits',
        'become_merchant'     => 'Become a Merchant',
        'blog'             => 'Blog',
        'categories'     => 'Categories',
        'contact_us'     => 'Contact us',
        'contact_seller'     => 'Contact Seller',
        'customer_service'     => 'Customer Service',
        'dashboard'        => 'Dashboard',
        'faq'             => 'FAQ',
        'gift_cards'    => 'Gift cards',
        'home'             => 'Home',
        'how_it_works'     => 'How It Works',
        'lang'             => 'Lang',
        'let_us_help'     => 'Let Us Help You',
        'make_money'     => 'Make Money',
        'menu'             => 'Menu',
        'merchant_dashboard' => 'Merchant Dashboard',
        'my_account'    => 'My Account',
        'my_orders'        => 'My Orders',
        'my_wishlist'    => 'My Wishlist',
        'my_coupons'    => 'My Coupons',
        'my_addresses'    => 'My Addresses',
        'open_dispute'     => 'Open Dispute',
        'privacy_policy' => 'Privacy Policy',
        'return_and_refund_policy'     => 'Return and Refund Policy',
        'refunds_disputes'             => 'Disputes',
        'sell_on'         => 'Sell On :platform',
        'support'         => 'Support',
        'term_and_conditions' => 'Terms and Conditions',
        'wishlist'         => 'Wishlist',
        'your_account'     => 'Your Account',
        'your_orders'     => 'Your Orders',
        'toggle_navigation' => 'Toggle navigation',
    ],
    'button' => [
        'add_to_cart'         => 'Add to Cart',
        'add_all_to_cart'         => 'Add all to Cart',
        'add_to_wishlist'     => 'Add to wishlist',
        'add_new_address'   => 'Add New Address',
        'apply_coupon'        => 'Apply Coupon',
        'appeal'            => 'Appeal',
        'clear_all'               => 'Clear All',
        'compare'                 => 'Compare',
        'buy_now'             => 'Buy Now',
        'buy_from_this_seller' => 'Buy From This Seller',
        'cancel'            => 'Cancel',
        'change_avatar'     => 'Change Avatar',
        'checkout'             => 'Checkout',
        'choose_plan'         => 'Choose plan',
        'choose_from_categories' => 'Choose From Categories',
        'clear'             => 'Clear',
        'contact'             => 'Contact',
        'contact_seller' => 'Contact Seller',
        'continue_shopping' => 'Continue Shopping',
        'confirm_goods_received' => 'Confirm Goods Received',
        'delete'             => 'Delete',
        'details'             => 'Details',
        'go_back'            => 'Go Back',
        'give_feedback'     => 'Give Feedback',
        'login'             => 'Login',
        'login_with_fb'     => 'Login with Facebook',
        'facebook'          => 'Facebook',
        'google'          => 'Google',
        'login_with_g'         => 'Login with Google',
        'ok'                => 'OK',
        'open'                => 'Open',
        'open_dispute'         => 'Open Dispute',
        'order_detail'         => 'Order Detail',
        'pay_now'            => 'Pay Now',
        'proceed'             => 'Proceed',
        'proceed_to_checkout' => 'Proceed to Checkout',
        'quick_view'         => 'Quick View',
        'quick_checkout'     => 'Quick Checkout',
        'read_more'            => 'Read more',
        'recover_password'     => 'Recover Password',
        'response'            => 'Response',
        'refund_request'     => 'Refund Request',
        'return_goods'        => 'Return Goods',
        'remove'             => 'Remove',
        'remove_from_wishlist' => 'Remove from Wishlist',
        'save'                 => 'Save',
        'selling'             => 'Start Selling',
        'send_message'         => 'Send Message',
        'send_password_link' => 'Send Password Reset Link',
        'shop_now'             => 'Shop Now',
        'shop_from_other_categories' => 'Shop From Other Categories',
        'subscribe'         => 'Subscribe',
        'submit'            => 'Submit',
        'track_order'         => 'Track Order',
        'update'             => 'Update',
        'update_cart'         => 'Update Cart',
        'upload_photo'         => 'Upload Photo',
        'view_product_details' => 'View Product Details',
        'view_dispute'        => 'View Dispute',
        'visit_store'         => 'Visit Shop',
        'place_order'         => 'Place Order',
    ],

    //Store Front
    'account' => 'Account',
    'account_login' => 'Account Login',
    'active_listings' => 'Active listings',
    // 'actions' => 'Actions',
    'address' => 'Address',
    'addresses' => 'Addresses',
    'alert' => 'Alert',
    'all_categories' => 'All Categories',
    'all_items' => 'All Items',
    'all_orders' => 'All Orders',
    'amount' => 'Amount',
    'and' => 'And',
    'appeal_dispute' => 'Appeal Dispute',
    'attachment' => 'Attachment',
    'attributes' => 'Attributes',
    'attribute' => 'Attribute',
    'avatar' => 'Avatar',
    'availability' => 'Availability',
    'basic_info' => 'Basic Info',
    'basic_packaging' => 'Free Basic Packaging',
    'best_match' => 'Best Match',
    'billing_detail' => 'Billing Detail',
    'billing_address' => 'Billing Address',
    'bio'    => 'Detail',
    // 'bought_together' => 'Frequently bought together',
    'brand' => 'Brand',
    'by'    => 'By',
    'category' => 'Category',
    // 'cart_item_count' => ':count Items',
    'cart_summary' => 'Cart Summary',
    'card' => 'Card',
    'change_password' => 'Change Password',
    'checkout' => 'Checkout',
    'condition' => 'Condition',
    'connect' => 'Connect With :',
    'confirmation' => 'Confirmation!',
    'confirm_password' => 'Confirm Password',
    'contact_us'     => 'Contact us',
    'coupon' => 'Coupon',
    'coupons' => 'Coupons',
    'coupon_code' => 'Coupon Code',
    'coupon_off' => ':value Off',
    'create_account' => 'Create Account',
    'credit_card' => 'Credit Card',
    'custom_shipping' => 'Custom shipping',
    'customer_reviews' => 'Customer Reviews',
    'current_password' => 'Current Password',
    'dashboard'    => 'Dashboard',
    'date' => 'Date',
    'days' => ':count Day|:count Days',
    'description' => 'Description',
    'discount' => 'Discount',
    'dispute' => 'Dispute',
    'disputes' => 'Disputes',
    'dispute_detail' => 'Dispute Detail',
    'dispute_finished' => 'Dispute Finished',
    'disputed' => 'Disputed',
    'dob' => 'Date of Birth',
    'email' => 'Email',
    'empty_cart' => 'Your Cart is Empty!',
    'empty_wishlist' => 'Your Wishlist is Empty! What are you doing?',
    'error' => 'ERROR!',
    'estimated_delivery_time' => 'Estimated Delivery Time',
    'expired' => 'Expired',
    'expired_at' => 'Expired at',
    'feedback' => 'Feedback',
    'feedbacks' => 'Feedbacks',
    'forgot_password' => 'Forgot Password?',
    'free_shipping' => 'Free Shipping',
    'full_name' => 'Full Name',
    'gift_cards' => 'Gift Cards',
    'goods_received' => 'Goods received',
    'grid_view' => 'Grid View',
    'has_offers' => 'Has offers',
    'hate_it' => 'Hate it',
    'have_account' => 'Already Member? Login Here',
    'have_an_account' => 'I already have an account',
    'hello' => 'Hello',
    'home' => 'Home',
    'how_was_the_product' => 'How was the product?',
    'how_satisfied_you_are' => 'How satisfied you are with this seller?',
    'high_to_low' => 'High to Low',
    'image' => 'Image',
    'in_stock' => 'In Stock',
    'invalid' => 'Invalid',
    'item_count' => 'Item count',
    'items_sold' => 'Items sold',
    'its_ok' => 'It\'s OK',
    'latest_reviews' => 'Latest reviews',
    'leave_message_to_seller' => 'Leave a message for the seller',
    'lifetime' => 'Lifetime',
    'like_it' => 'Like it',
    'list_view' => 'List View',
    'logo' => 'Logo',
    'logout' => 'Logout',
    'love_it' => 'Love it',
    'low_to_high' => 'Low to High',
    'main_searchbox_placeholder' => 'I\'m shopping for...',
    'member_since' => 'Member since',
    'manage_your_account' => 'Manage Account',
    'marketplace_steps_in' => ':marketplace Steps In',
    'me' => 'Me',
    'message_history' => 'Message History',
    'message_from_seller' => 'Message From the Seller',
    'most_popular' => 'Most popular',
    // 'my_account' => 'My account',
    // 'my_orders' => 'My orders',
    // 'my_inbox' => 'Inbox',
    'name' => 'Name',
    'new_arrivals' => 'New Arrivals',
    'new_password' => 'New Password',
    'new_messages' => 'New Messages',
    'new' => 'New',
    'newest' => 'Newest',
    'nice_name' => 'Nickname',
    'no' => 'No',
    'no_reviews' => 'No reviews',
    'no_product_found' => 'No product found',
    'not_so_good' => 'Not so good',
    'note' => 'Note',
    'notice' => 'Notice!',
    'nothing_found' => 'Nothing found',
    'no_order_history' => 'You don\'t have any orders!',
    'oldest' => 'Oldest',
    'open_a_dispute' => 'Open a Dispute',
    'options' => 'Options',
    'order' => 'Order',
    'orders' => 'Orders',
    'digital_orders' => 'Digital Orders',
    'order_amount' => 'Order Amount',
    'order_detail' => 'Order Detail',
    'order_info' => 'Order info',
    'order_id' => 'Order ID',
    'order_time_date' => 'Order date and time',
    'order_received' => 'Order received',
    'out_of_stock' => 'Out Of Stock',
    'packaging' => 'Packaging',
    'packaging_cost' => 'Packaging Cost',
    'password_recovery' => 'Password Recovery',
    'password_reset' => 'Password reset',
    'payment' => 'Payment',
    'payment_detail' => 'Payment Detail',
    'payment_instruction' => 'Payment instruction',
    'payment_method' => 'Payment Method',
    'payment_options' => 'Payment Options',
    'payment_status' => 'Payment Status',
    'per_month' => '/month',
    'percent_off' => ':value% Off',
    'percnt_off' => '% Off',
    'phone' => 'Phone',
    'popular' => 'Popular',
    'price' => 'Price',
    'price_above' => ':value & Above',
    'price_under' => 'Under :value',
    'product' => 'Product',
    'products' => 'Products',
    'product_desc' => 'Product Description',
    'product_desc_seller' => 'Seller Specifications',
    'published_at' => 'Published',
    'quantity' => 'Quantity',
    'rating' => 'Rating',
    'reason' => 'Reason',
    'recent_posts' => 'Recent posts',
    'refund_amount' => 'Refund Amount',
    'refurbished' => 'Refurbished',
    'register' => 'Register',
    'register_here' => 'Register here',
    'remember_me' => 'Remember Me',
    'remember_card_for_future_use' => 'Remember the card for future use',
    'remove_item' => 'Remove Item',
    'reply' => 'Reply',
    'return_goods' => 'Return Goods',
    'return_and_refund_policy'     => 'Return and Refund Policy',
    'review' => 'Review',
    'reviews' => '{0,1} :count Review|{2,*} :count Reviews',
    'running' => 'Running',
    'search' => 'Search',
    'search_results' => 'Search Results',
    'select' => 'Select',
    'select_product' => 'Select Product',
    'select_reason' => 'Select Reason',
    'seller' => 'Seller',
    'seller_doesnt_ship' => 'This seller doesn\'t ship to your selected Country/Region',
    'seller_info' => 'Seller Info',
    'seller_helps_you' => 'Seller Helps You',
    'serial_number' => 'Serial Number',
    'share_on' => 'Share on :name',
    'ship_to' => 'Deliver to',
    'shipping' => 'Shipping',
    'shipping_options' => 'Shipping options',
    'shipping_address' => 'Shipping Address',
    'shipping_cost' => 'Shipping Cost',
    'shop_by' => 'Shop by',
    'shopping_cart' => 'Shopping Cart',
    'sing_in' => 'Hello, Sign in',
    'sold_by' => 'Sold by',
    'sort_by' => 'Sort by',
    'stock_count' => ':count in stock',
    'store' => 'Shop',
    'shop_down' => 'This shop is unavailable right now',
    'store_not_available' => 'Shop not available',
    // 'store_profile' => 'Shop Profile',
    'stuff_pick' => 'Staff Pick',
    'status' => 'Status',
    'stay_connected' => 'Stay Connected',
    'subtotal' => 'Subtotal',
    'subscription' => 'Subscription',
    'support_partial_use' => 'Support Partial Use',
    'tags' => 'Tags',
    'taxes' => 'Taxes',
    'title' => 'Title',
    'to' => 'To',
    'toll_free' => 'Toll-free',
    'total' => 'Total',
    'to_location' => ' to <em>:location</em>',
    'unit_price' => 'Unit Price',
    'used' => 'Used',
    'use_before' => 'Use before',
    'use_between' => 'Use between',
    'validity' => 'Validity',
    'valid_from' => 'Valid from',
    'value' => 'Value',
    'verified_purchase' => 'Verified Purchase',
    'view_detail' => 'View Detail',
    'view_more_offers' => 'View (:count) more offers',
    'warning' => 'Warning!',
    'when_min_order_value' => 'Minimum order value of :value or more',
    'wishlist' => 'Wishlist',
    'write_your_message' => 'Write your message',
    'years_count' => '{0,1} :count Year|{2,*} :count Years',
    'yes' => 'Yes',
    'your_account' => 'Your Account',
    'your_cart' => 'Your Cart',
    'your_order_history' => 'Your orders',
    'your_digital_order_history' => 'Your digital orders',
    'up' => 'Up',
    'help' => [
        'be_honest_when_leave_feedbacks' => 'Your feedback will help other shoppers to make their decision. So, be wise, sincere and honest to leave your feedback.',
        'card_exp_month' => 'Select expiration month',
        'card_exp_year' => 'Select expiration year',
        'create_account_on_checkout' => 'By creating the account you agree to our <a href=":link" target="_blank">terms and conditions</a>. Enter a secure password to login your account.',
        'customer_paid' => 'You paid <strong><em> :amount </em></strong>, inclusive all taxes, shipping charges and others.',
        'enter_cardholder_name' => 'Please enter cardholder name',
        'first_step' => 'First Step',
        'give_tracking_number_here' => 'Give The Tracking ID',
        'give_all_feedbacks_together' => 'Give All Feedbacks Together',
        'how_to_open_a_dispute_first_step' => 'Before opening a dispute, we recommend you to contact the seller to discuss about the issue. Most of the case seller will help to solve the issue.',
        'how_to_open_a_dispute_second_step' => 'You can choose between two options: <br/><br/><em>Refund Only</em> – this means that either you did not receive the item and you’re applying for a full refund or you did receive the item and you want a partial refund (without having to send the item back), OR <br/><br/><em>Return Goods</em> – this means that you want to return the item and apply for a full refund.',
        'how_to_open_a_dispute_third_step' => 'If you and seller can\'t come to an agreement, you can appeal the dispute to review. This point we will step in and help.',
        'order_refunded' => 'Previously refunded <strong><em> :amount </em></strong> of total <strong><em> :total </em></strong>',
        'reason_to_return_goods' => '- I am not satisfied with the product I received and would like to return it for a full refund. <small>(You may have to pay the return shipping cost)</small>',
        'reason_to_refund_request' => '- I haven\'t received my order and I would like to get my money back.<br/>- The product is not as described and I would like a partial refund.',
        'return_goods_help_txt' => 'If the seller accepts your proposal, you will be asked to return the item(s) you received and provide a return tracking number.',
        'required_fields' => 'marked fields are required',
        'second_step' => 'Second Step',
        'third_step' => 'Third Step',
        'upload_photo' => 'You can upload only 1 file not more than 2MB in size and supports JPG, JPEG or PNG',
        'when_marketplace_steps_in' => 'If you and the seller can\'t come to an agreement, we will step in and help.',
    ],

    'defaults' => [
        'new_message_from' => 'New message from :sender',
    ],

    'notify' => [
        'address_created' => 'Address saved successfully!',
        'address_deleted' => 'Address deleted successfully!',
        'already_subscribed' => 'Cool! You have already subscribed!',
        'are_you_sure' => 'Are you sure you want to do this?',
        'authentication_failed' => 'Authentication failed. :msg',
        'authentication_successful' => 'Cool! Authentication successful. Create your account.',
        'calculating' => 'Calculating',
        'canceled'  => 'Canceled!',
        'cant_deliver' => 'Can not deliver',
        'confirmed' => 'Confirmed',
        'cart_updated' => 'Cart updated',
        'cart_empty'  => 'Cart is empty!',
        'coupon_applied' => 'Coupon applied successfully!',
        'coupon_limit_expired' => 'This coupon has been used!',
        'coupon_min_order_value' => 'Invalid coupon! The coupon is not valid for this amount of order. Shop more to get discounts!',
        'coupon_not_valid_for_zone' => 'This coupon is not valid for your area!',
        'coupon_not_valid' => 'Invalid coupon!',
        'coupon_not_exist' => 'Coupon does not exist!',
        'will_calculated_on_select' => 'Will be calculated after you select all properties',
        'dispute_created' => 'Dispute created successfully!',
        'dispute_updated' => 'Dispute updated successfully!',
        'failed' => 'Action failed! Something went wrong!!',
        'fill_required_info' => 'Please fill all the required information!',
        'info_updated' => 'Updated successfully!',
        'info_deleted' => 'Deleted successfully!',
        'invalid_request' => 'Invalid request!',
        'item_added_to_cart' => 'Item added to cart',
        'items_added_to_cart' => 'Items added to cart',
        'item_added_already_in_cart' => 'Item already in your cart',
        'item_added_to_wishlist' => 'Item added to wishlist',
        'item_added_already_in_wishlist' => 'Item already in your wishlist',
        'item_removed_from_cart' => 'Item removed',
        'item_removed_from_wishlist' => 'Item removed',
        'logged_in_successfully' => 'Logged in successfully!',
        'logged_out_successfully' => 'Logged out successfully!',
        'max_item_stock' => 'This seller has limited stock available',
        'message_sent' => 'Message sent successfully!',
        'minimum_order_qtt_reached' => 'You can\'t place an order below this quantity',
        'nothing_found' => 'Nothing found!',
        'order_creation_failed' => 'Something went wrong!! Please try again!',
        'order_placed' => 'Order placed successfully!',
        'order_placed_thanks' => 'Thank you very much! Your order has been placed successfully!',
        'order_updated' => 'Order updated successfully!',
        'order_will_ship_to' => 'We\'ll send the items very soon to this address',
        'payment_method_config_error' => 'This seller can\'t accept payment using this payment method! Choose from other options or buy from other sellers.',
        'payment_failed' => 'The payment has been failed!! More details can be found in the log.',
        'please_login_to_checkout' => 'Please login to checkout!',
        'refund_request_sent' => 'Refund request sent!',
        'select_shipping_address' => 'Select shipping address.',
        'shipping_cost_may_change' => 'Shipping cost may change for the new destination.',
        'seller_doesnt_ship' => 'This seller does not deliver to your selected Country/Region. Change the shipping address or find other sellers who ship to your area.',
        'seller_has_no_payment_method' => 'This seller has no active payment method to accept payment. You may find similar items from other sellers.',
        'store_not_available' => 'This seller is inactive or not exist in the marketplace. You may find similar items from other sellers.',
        'subscribed' => 'Cool! You have subscribed successfully!',
        'we_dont_save_card_info' => 'We do not store your card information.',
        'you_will_be_redirected_to_paypal' => 'You will be redirected to PayPal to securely complete your payment.',
        'your_feedback_saved' => 'Thank you for your feedback!',
        'you_will_be_redirected_to_paystack' => 'You will be redirected to Paystack.',
        'you_will_be_redirected_to_instamojo' => 'You will be redirected to Instamojo.',
        'all_seller_doesnt_ship' => 'All seller does not deliver to your selected Country/Region. Change the shipping address or find other sellers who ship to your area.',
        'pick_up_order_from' => 'Pick order from this address',
        'availability' => 'Availability',
        'business_days' => 'Business Days',
        'order_number' => 'Show this order number when pickup the order',
        'account_delete' => 'Your account has been deleted successfully',
        'no_item_listed' => 'No item listed on this platform yet',
        'switched_to_merchant_successfully' => 'Switched to merchant successfully',
        'merchant_acc_not_exist' => 'Merchant account not exist',
        'business_days_not_given' => 'Shops business days not given',
        'select_pickup_address' => 'You must select a pickup address to place your order',
    ],

    'input_label' => [
        'agree' => 'I agree',
        'i_agree_with_terms' => 'I agree with the <u><a href=":url" target="_blank"> terms</a></u>',
        'subscribe_to_the_newsletter' => 'Subscribe to the Newsletter',
    ],

    'placeholder' => [
        'address_line_1' => 'Address',
        'address_line_2' => 'Apartment, unit, suite, or floor',
        'address_title' => 'Contact person',
        'address_type' => 'Address type',
        'bio' => 'Little more about yourself',
        'card_cvc' => 'CVC',
        'card_exp_month' => 'Expiration month',
        'card_exp_year' => 'Expiration year',
        'cardholder_name' => 'Cardholder name',
        'card_number' => 'Card number',
        'city' => 'City',
        'confirm_password' => 'Confirm password',
        'contact_us_subject' => 'What is your purpose to contact',
        'current_password' => 'Current password',
        'description' => 'Description',
        'dob' => 'YYYY-MM-DD',
        'email' => 'Please enter your email',
        'exp_month' => 'Expiration month',
        'exp_year' => 'Expiration year',
        'full_name' => 'Full name',
        'have_coupon_from_seller' => 'I\'ve a coupon from this seller',
        'message' => 'Write your message within 500 characters',
        'message_to_seller' => 'Write your message or instructions to the seller.',
        'name' => 'Your name',
        'nice_name' => 'Nice name',
        'password' => 'Password',
        'phone_number' => 'Contact number',
        'search' => 'Search',
        'select_payment_option' => 'Choose the payment option',
        'state' => 'State/Province/Region',
        'your_email' => 'Your email',
        'refund_amount' => 'Refund amount',
        'valid_email' => 'Enter a valid email address',
        'write_your_feedback' => 'Write your feedback within 250 characters',
        'zip_code' => 'Zip code',
    ],

    'validation' => [
        'dispute_type_id_required' => 'Select dispute type',
        'dispute_product_id_required_with' => 'Select the products that have an issue.',
        'feedback_rating_issue' => 'Feedback ratings must be in 1 to 5',
        'feedback_comment_between' => 'Comments must be between :min to :max characters.',
        'incorrect_current_password' => 'Your current password is not correct!',
        'address_title' => 'Please include first name and last name in the \'Contact Person\' input field.'
    ],

    'confirm_action' => [
        'cant_undo' => 'Are you sure? <br/>You can\'t undo this action',
        'delete' => 'Are you sure want to delete this resource? <br/>You can\'t undo this action!',
        'goods_received' => 'Are you sure? <br/>You can\'t undo this action',
        'open_a_dispute' => 'Before opening a dispute, please try to solve the issue with the seller.',
    ],

    'section_headings' => [
        'additional_items' => 'Additional items <span class="text-primary">to explore</span>',
        'contact_seller' => 'Leave a message for the seller',
        'contact_form' => 'Contact form',
        'frequently_bought_together' => 'Frequently <span class="text-primary">Bought Together</span>',
        'give_feedbacks_to_products' => 'Give Feedbacks to Products',
        'give_feedbacks_to_seller' => 'Give Feedback to Seller',
        'how_to_give_feedbacks' => 'How to Give Feedbacks',
        'how_to_open_a_dispute' => 'How to Open a Dispute',
        'key_features' => 'Key <span class="text-primary">Features</span>',
        'recently_added' => 'Recently <span class="text-primary">Added</span>',
        'recently_viewed' => 'You Viewed <span class="text-primary">Recently</span>',
        'best_selling_now' => 'Best Selling <span class="text-primary">Now</span>',
        'related_items'    => 'Related <span class="text-primary">Items</span>',
        'select_from_categories' => 'Select From <span class="text-primary">Categories</span>',
        'weekly_popular' => 'Weekly <span class="text-primary">Popular</span>',
        'monthly_popular' => 'Monthly <span class="text-primary">Popular</span>',
        'you_may_also_like' => 'You may also like',
        'trending_now' => 'Trending <span class="text-primary">Now</span>',
    ],

    'bundle_offer' => 'Bundle Offers',
    'you_may_also_like' => 'You may also like',
    'featured' => 'Featured',
    'neckbands' => 'Neckbands',
    'flash_deal' => 'Flash Deal',
    'flash_deals' => 'Flash Deals',
    'offer_end_in' => 'Offer Ends In',
    'ends_in' => 'Ends In',
    'flash_sale' => 'Flash Sale',
    'sold_item_qtt' => ':item sold',
    'top_rated' => 'Top Rated',
    'key_features' => 'Key Features',
    'recently_added' => 'Recently Added',
    'recently_viewed' => 'You Viewed Recently',
    'best_selling_now' => 'Best Selling Now',
    'related_items'    => 'Related Items',
    'alternative_products' => 'Alternative Products',
    'select_from_categories' => 'Categories',
    'weekly_popular' => 'Weekly Top',
    'monthly_popular' => 'All Time Best Seller',
    'deal_of_the_day' => 'Deal of the day',
    'best_find_under' =>  'Best finds under :amount',
    'featured_category' =>  'Featured Category',
    'featured_brand' =>  'Featured Brands',
    'featured_vendor' =>  'Featured Vendors',
    'featured_categories' => 'Featured Categories',

    'benefit' => [
        'one' => [
            'title' => 'Start Selling in Minutes',
            'icon' => 'rocket', // Don't translate this line
            'detail' => 'Get straight to growing your business. We handle everything to sell online. So you can focus on your business rather managing the infrastructure.',
        ],
        'two' => [
            'title' => 'Comprehensive Dashboard',
            'icon' => 'tablet', // Don't translate this line
            'detail' => 'Get a holistic and detailed view of your business to better understand sales, orders, and customers to better tailor your products.',
        ],
        'three' => [
            'title' => 'Get Paid Directly and Securely',
            'icon' => 'credit-card', // Don't translate this line
            'detail' => 'We don\'t keep your money! The payment goes to you directly as soon as a buyer pays.',
        ],
    ],

    'plan' => [
        'inventory_limit' => ':limit Products',
        'marketplace_commission' => ':commission% Marketplace Commission',
        'no_transaction_fee' => 'No Transaction Fees',
        'no_marketplace_commission' => 'No Marketplace Commission',
        'team_size' => ':size Staff Users',
        'transaction_and_commission' => ':commission% + :fee Per Transaction',
        'transaction_fee' => ':fee Per Transaction',
    ],

    'how_it_work_steps' => [
        'step_1' => [
            'title' => 'Register as Merchant',
            'detail' => 'Creating an account is very simple, Your merchant dashboard will be ready as soon as you\'re registered. You can manage everything that belongs to your store and running the business.',
        ],
        'step_2' => [
            'title' => 'List Your Items',
            'detail' => 'Listing your products is really simple through our easy to use the self-serve portal. Upload including high-quality product images and additional details.',
        ],
        'step_3' => [
            'title' => 'Sell & Fulfill Orders',
            'detail' => 'After you list your offers, customers can visit them on the Marketplace. We\'ll notify you when customers place an order. Fulfill the order on promised time is very important.',
        ],
        'step_4' => [
            'title' => 'Get Paid Instantly',
            'detail' => 'We don\'t hold your money, the payment will be sent to your connected account directly.',
        ],
        'ending' => 'Be Part <br/>of Our <br/>Story!',
    ],

    'intro_lead' => 'Everything you need to start selling online!',
    'intro_heading' => 'It\'s made for you',
    'selling_price_tagline' => 'Starting :price a month + additional fees',

    'benefits' => 'Benefits',
    'faq' => 'FAQ',
    'how_it_works' => 'How It Works',
    'pricing' => 'Pricing',

    // Version 1.2.4
    'first_listed_on' => 'Date first listed on :platform',
    'created_at' => 'Created at',
    'dispute_details' => 'Dispute Details',
    'manufacturer' => 'Manufacturer',
    'min_order_quantity' => 'Minimum order quantity',
    'total_sold_quantity' => 'Total sold quantity',
    'model_number' => 'Model number',
    'mpn' => 'Part Number',
    'origin' => 'Origin',
    'refunds' => 'Refunds',
    'refund_details' => 'Refund Details',
    'shipping_weight' => 'Shipping weight',
    'sku' => 'Seller SKU',
    'technical_details' => 'Technical Details',
    'updated_at' => 'Updated at',
    'success' => 'Success',
    'info' => 'Info',
    'danger' => 'Error',
    // Version 1.3.0
    'enter_tracking_number' => 'Enter your order tracking number.',
    'from_verified_seller' => 'Verified seller',
    'mark_as_solved' => 'Mark as solved',

    // Version 2.0.0
    'add_shipping_address' => 'Add a shipping address',
    'archive' => 'Archive',
    'country' => 'Country',
    'delivery_locations_info' => 'Delivery options and delivery speeds may vary for different locations',
    'choose_delivery_location' => 'Choose your location',
    'edit' => 'Edit',
    'edit_account' => 'Edit My Account',
    'invoice' => 'Invoice',
    'message_archived' => 'Message has been archived successfully!',
    'my_messages' => 'Messages',
    'of_total' => ':first - :last of :total',
    'or' => 'OR',
    'subject' => 'Subject',
    'unread_messages' => 'Unread messages',
    'std_delivery_time' => 'Standard delivery time',
    'cancel_order'         => 'Cancel Order',
    'canceled'         => 'Canceled',
    'order_canceled' => 'The order has been canceled!',
    'order_again' => 'Order Again',
    'user' => 'User',
    'free' => 'Free',
    'more_items_from_this_seller' => 'More items from :seller',
    'order_cancellation_requested' => 'Cancellation request has been saved.',
    'order_return_requested' => 'Return request has been saved.',
    'return' => 'Return',

    // Version 2.1.0
    'chat_welcome' => 'Hey! Have a question?',
    'connecting' => 'Connecting...',
    'login_to_chat' => 'We\'re just one step away! Please login to start the conversation.',
    'now' => 'Now',
    'offline' => 'Offline',
    'online' => 'Online',
    'shop_not_found' => 'Shop not found. Check other vendors.',
    'session_expired' => 'The session has been expired! Please refresh the page.',
    'item_not_available' => 'This item is no more available!',
    'cancel_items' => 'Cancel Items',
    'items' => 'Items',
    'cancellation' => 'Cancellation',
    'order_cancel_msg_title' => 'Cancellation isn\'t guaranteed!',
    'order_cancel_msg' => 'We\'re preparing this order for shipment. We\'ll do our best to cancel the requested item(s).',
    'order_return_msg_title' => 'Don\'t send the items yet!',
    'order_return_msg' => 'If the seller accepts your request, you will be asked to return the item(s) you received and provide a return tracking number.',
    'cancellation_requested' => 'Cancellation requested',
    'cancellation_reason_required' => 'Select cancellation reason',
    'send_request' => 'Send Request',
    'return_items' => 'Return Items',
    'return_requested' => 'Return requested',
    'cancelled' => 'Cancelled',
    'returned' => 'Returned',
    'select_cancel_items_required' => 'Select the product(s) you want to cancel.',
    'sale_count' => 'Sold: :count',
    'login' => 'Sign In',
    'weekly_offer' => 'Offers of the Week',
    'see_more' => 'See More',
    'more_from_shop' => 'More Products from this Shop',
    'welcome' => 'Welcome To',
    'support' => 'Support',
    'wallet' => 'Wallet',
    'new_item' => 'New',
    'stock' => ':stock In Stock',
    'hot' => 'Hot',
    'shop_now' => 'Shop Now',
    'add_to_cart' => 'Add to Cart',
    'item_added_to_wishlist' => 'Item added to wishlist',
    'flash_deal_days' => 'days',
    'hrs' => 'hrs',
    'mins' => 'mins',
    'sec' => 'sec',
    'today_popular' => 'Today\'s Trend',
    'additional_item' => 'Additional items to explore',
    'track_your_order' => 'Track Your Order',
    'brands' => 'Brands',
    'vendors' => 'Vendors',
    'all_brands' => 'All Brands',
    'all_shops' => 'All Vendors',
    'profile' => 'Profile',
    'product_videos' => 'Product videos',
    'events' => 'Events',
    'contact_info' => 'Contact Info',
    'pickup' => 'Pickup',
    'inquiry_basket' => 'Inquiry Basket',
    'join_free' => 'Join Free',
    'sign_in' => 'Sign In',
    'you_may_like' => 'You May Like',
    'product_count' => ':product + products',
    'hot_products' => 'Hot Products',
    'trending_products' => 'Selected Trending Products',
    'source_now' => 'Source Now',
    'trending_now' => 'Trending Now',
    'sale_over' => 'Sorry! You\'ve missed the sale!',
    'categories' => 'Categories',
    'messages' => 'Messages',
    'sell' => 'Sell',
    'cart_update_failed' => 'Cart update failed!',
    'cart_count' => 'Cart count',
    'listings_count' => ':count items',
    // 'follow_us' => 'Follow Us',
    'for_customers' => 'For Customers',
    'search_product_and_supplier' => 'Search Products & Suppliers',
    'quick_links' => 'Quick Links',
    'language_options' => 'Language Options',
    'message' => 'Message',
    'from' => 'From',
    'type_min_char'  => 'Type at least :min characters.',
    'searching' => 'Searching',
    'for_customer' => 'For Customer',
    'for_vendor' => 'For Vendor',
    'search_products_suppliers' => 'Search Products & Suppliers',
    'hot_categories' => 'Hot categories',
    'legal_name' => 'Legal name',
    'std_shipping_carrier' => 'Standard shipping carrier',
    'country_not_exist' => 'The country you have chosen is not exist in the database.',
    'login_with_social' => 'or use your social media account',
    'invalid_address' => 'The address is not valid. Please enter a valid address.',
    'change_shipping_location' => 'Change the shipping location, It may change the available shipping options and cost of shipping.',
    //mobile-menu
    'settings' => 'Settings',
    'menu_options' => 'Menu Options',
    'languages' => 'Languages',
    'condition_note' => 'Condition note',
    'offer_price' => 'Offer price',
    'seller_rating' => 'Seller rating',
    'stock_quantity' => 'Stock quantity',
    'gtin' => 'GTIN',
    'has_return_policy' => 'Has return policy',
    'offer_ends_at' => 'Offer ends at',
    'pickup_time' => 'Pickup Time',
    'pickup_from' => 'Pickup From',
    'your_wishlist' => 'Your Wishlist',
    'your_comparisons' => 'Your Comparisons',
    'comparisons' => 'Comparisons',
    'buy_from_vendor' => 'Buy from :vendor',
    'digital_product' => 'Digital Product',
    'digital_products' => 'Digital Products',
    'download' => 'Download',
    'downloadable' => 'Downloadable',
    'downloadables' => 'Downloadable\'s',
    'physical_goods' => 'Physical goods',
    'download_left' => 'You have :download_number download attempt left. Maximum download number is :download_limit',
    'product_type' => 'Product Type',
    'applicable_taxes_for' => 'Applicable taxes for',
    'cart_items' => 'Cart items',
    'download_links_of' => 'Download links of',
    'copy_to_clipboard' => 'Copy to clipboard',
    'order_date' => 'Date',
    'dont_show' => 'Don\'t show again',
    'visit_shop_page' => 'Visit shop page',
    'shop_home' => 'Shop home',
    'top_selling' => 'Top selling',
    'filters' => 'Filters',
    'clear_all_filters' => 'Clear all filters',
    'show_more' => 'Show more',
    'show_less' => 'Show less',
    'show_product_page' => 'Visit the product page.',
    'slider_image' => 'Slider Image',
    'featured_items' => 'Featured Items',
    'qtt_sold_of' => ':sold of :qtt sold',
    'system_picked_item' => 'Specially Picked For You',
    'listed_at' => 'Listed at',
    'apply'        => 'Apply',
    'newsletter_subscribe' => 'Subscribe to our Newsletter',
    'newsletter_description' => 'Signup for our newsletter to get the latest news, updates and amazing offers delivered directly in your inbox.',
    'total_items' => 'Total Items',
    'price_for_all' => 'Total:',
    'view_merchant_dashboard' => 'View Merchant Dashboard',
    'customer_address' => 'Customer Address',
    'no_pickup_options' => 'This seller does not offer a pickup option.',
    'maximum_download_limit_reached' => 'You have reached maximum download limit',
    'trust_badge' => 'Trust Badge',
    'coupon_code_size' => 'Coupon code size',
];
