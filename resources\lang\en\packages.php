<?php

return [
  // 4. Wallet
  'wallet' => [
    'alert' => 'Alert',
    'allow_customer_wallet_checkout' => 'Allow customers to checkout using wallet balance',
    'amount' => 'Amount',
    'approval' => 'Approval',
    'approved' => 'Approved',
    'payout_amount' => 'Payout amount',
    'fee' => 'Fee',
    'approve' => 'Approve',
    'available_balance' => 'Available balance',
    'balance' => 'Balance',
    'confirmed_invalid' => 'The translation has already been confirmed',
    'confirmed' => 'Confirmed',
    'completed' => 'Completed',
    'coupon_code' => 'Coupon code',
    'credit_card' => 'Credit card',
    'date' => 'Date',
    'decline' => 'Decline',
    'declined' => 'Declined',
    'deposit' => 'Deposit',
    'description' => 'Description',
    'deposit_fund' => 'Deposit fund',
    'deposit_payment_methods' => 'Deposit payment methods',
    'deposit_description' => 'Deposit into the :marketplace digital wallet using :payment_method',
    'direct_deposit' => 'Direct deposit',
    'deposited_amount' => 'Deposited amount',
    'fund_deposited_using' => 'Deposited fund using :payment',
    'insufficient_funds' => 'Insufficient funds',
    'in_last_30_days' => 'In last 30 days',
    'next_payout_date' => 'Next payout date',
    'new_payout' => 'New payout',
    'bulk_payout' => 'Bulk payout',
    'bulk_upload' => 'Bulk upload',
    'wallet_bulk_upload' => 'Bulk Deposits',
    'bulk_upload_input_error' => 'Invalid input in uploaded csv',
    'bulk_deposit_description' => 'Deposited by admin on code',
    'manual_payment_info_cod' => 'Manual payment info cod',
    'manual_payment_info_wire' => 'Manual payment info wire',
    'manual_payment_instructions_cod' => 'Manual payment instructions cod',
    'manual_payment_instructions_wire' => 'Manual payment instructions wire',
    'minimum_withdrawal_limit' => 'Minimum withdrawal limit',
    'minimum_withdrawal_limit_amount' => 'Minimum withdrawal limit is :amount',
    'minimum_withdrawal_limit_help' => 'The minimum account balance a vendor will need to request a withdrawal',
    'order_amount_escrow_holding_duration' => 'Order escrow holding duration',
    'order_amount_escrow_holding_duration_help' => 'After this designated period, the accumulated ordered amount will be automatically credited to the vendor\'s wallet',
    'pay_now' => 'Pay now',
    'payment_success' => 'Payment has been made successfully',
    'payment_failed' => 'Payment has been failed, please try again',
    'pending_balance' => 'Pending balance',
    'owner_invalid' => 'You are not the owner of the wallet',
    'pending' => 'Pending',
    'price_positive' => 'The price should be positive',
    'product_stock' => 'The product is out of stock',
    'refunded_amount' => 'Refunded amount',
    'remaining_balance' => 'Remaining balance',
    'refund' => 'Refund',
    'refunded' => 'Refunded',
    'refund_of' => 'Refund of :order',
    'select_atleast_one_payment_option' => 'Select at least one option. Please remember, you have to configure the enabled payment methods to use it.',
    'transactions' => 'Transactions',
    'transaction_type' => 'Transaction type',
    'shop' => 'Shop',
    'status' => 'Status',
    'submit' => 'Submit',
    'success' => 'Success!',
    'unconfirmed_invalid' => 'Confirmation has already been reset',
    'updated_at' => 'Updated at',
    'wallet_owner' => 'Wallet owner',
    'wallet_empty' => 'Wallet is empty',
    'wallet_settings' => 'Wallet settings',
    'wallet_checkout' => 'Wallet checkout',
    'wallet_checkout_off_when_vendor_paid' => 'Wallet checkout can not be enabled when vendor paid directly!',
    'withdraw' => 'Withdraw',
    'payout_requested' => 'Payout request has been created',
    'my_wallet' => 'My Wallet',
    'payout_desc' => 'Payout from :platform wallet',
    'payout' => 'Payout',
    'payouts' => 'Payouts',
    'select' => 'Select',
    'payout_request' => 'Payout request',
    'payout_requests' => 'Payout requests',
    'payout_approved' => 'The payout has been approved',
    'payout_declined' => 'The payout has been declined',
    'payout_fee' => 'Payout fee',
    'payout_fee_may_apply' => 'A payout fee may apply from :platform',
    'platform_fee' => 'Platform fee',
    'this_amount_will_charge' => 'This amount will charged on wallet',
    'proceed' => 'Proceed',
    'minimum_payout_limit_amount' => 'Minimum payout limit is :amount',
    'option' => 'Options',
    'type' => 'Type',
    'wallet' => 'Wallet',
    'transfer' => 'Transfer',
    'last_transfer' => 'Last transfer',
    'last_debited' => 'Last debited',
    'transfer_balance' => 'Balance Transfer',
    'transfer_to_wallet' => 'The receiver\'s email address',
    // 'transferred_balance' => 'Transferred Balance',
    'last_deposit' => 'Last deposit',
    'last_payout' => 'Last payout',
    'transfer_to' => 'Transfer To',
    'email_not_found' => 'The email you entered is not found',
    'no_transaction_found' => 'No transaction found',
    'transfer_to_help_text' => 'Enter the email address of the user you want to send the balance. The user must have to have a wallet to receive the balance.',
    'select_vendor_to_transfer_balance' => 'Select the vendor to transfer balance',
    'transfer_to_vendor_help_text' => 'Select the vendor you want to transfer the balance. The vendor must have a wallet to receive the balance.',
    'transfer_success' => 'Balance transferred successfully.',
    'wallet_email_not_found' => 'The email :email has not found.',
    'periodic_payout_created' => ':period payout has been created successfully.',
    'max_transfer_amount' => 'You can transfer maximum of :amount',
    'balance_sent_to' => 'Balance sent to :email',
    'balance_received_from' => 'Balance received from :email',
    'pay_by_wallet' => 'Pay from your wallet balance.',
    'purchase_of' => 'Purchase of order :order_numbers',
    'sufficient_balance' => 'Insufficient balance! Make a deposit into your wallet to continue.',
    'freeze_balance' => 'Freeze Balance',
    'transfer_self_customer' => 'Transfer to self customer',
    'transfer_self_merchant' => 'Transfer to self Merchant',
    'wallet_credit_reward_system' => 'Credit reward system',
    'wallet_credit_back_percentage' => 'Wallet credit back percentage',
    'wallet_release_credit_rewards_in_days' => 'Release credit rewards in',
    'when_empty_default_credit_reward_is_used' => 'If empty, default credit reward will be used.',
    'help_wallet_credit_reward_system' => 'Customers receive wallet credit rewards with every purchase.',
    'help_wallet_credit_back_inventory_form' => 'Enter the percentage of you want to rebate as wallet credit to customers. Keep empty/null to use the global shop config value.',
    'help_wallet_credit_back_config' => 'Enter the percentage of order value you want to rebate as wallet credit to customers. Set 0(zero) to disable global config value.',
    'help_release_credit_rewards_in' => 'Credits will be released automatically after the specified time period. Leave empty to disable auto-release, set to 0 for immediate release, or enter any other value.',
    'placeholder_release_credit_rewards_in' => 'Enter the number of days.',
    'days' => 'Days',
    'rewards' => 'Rewards',
    'credit_back_rewards' => 'Credit back rewards',
    'credit_rewards' => 'Credit rewards',
    'order_needs_to_be_paid' => 'Order needs to be paid to get rewards',
    'guest_customer_cant_get_reward' => 'Guest customer cat\'t get rewards',
    'create_an_account_to_get_reward' => 'Create an account to get the rewards',
    'get_percentage_credit_back' => 'Get :percentage% back as wallet credit.',
    'get_amount_credit_back' => 'Get :amount back as wallet credit.',
    'get_credit_back_rewards' => 'Get :percentage% cash back',
    'credit_back' => 'Credit back',
    'credit_back_for_order' => 'Credit back for order :order',
    'reward_credit_reversal' => 'Reward credit reversal',
    'initiated_at' => 'Initiated at',
    'release' => 'Release',
    'released' => 'Released',
    'delete' => 'Delete',

    // Mails
    'greeting' => "Hello :receiver",
    'update' => "Update Now",
    'see_now' => "See Now",
    'status' => "Status",
    'thanks' => 'Thank you',
    'approved' => 'Approved',
    'approved_amount' => 'Payout request of :amount has been approved.',
    'pending' => "Pending",
    'pending_subject' => 'Your payout request has been created. we are reviewing your request',
    'approve_subject' => 'Your payout request has been approved.',
    'declined_subject' => 'Your payout request has been declined.',
    'declined_msg' => 'We\'re sorry to say that your payout request has been declined for some reason. Please contact us for details',
    'pending_amount' => 'We are currently reviewing your payout request :amount created by you or your team.',
    'approve_amount' => 'We are approved your payout request :amount created by you or your team.',
    'deposit_subject' => 'Your deposit request has been Completed successfully.',
    'deposit_amount' => 'Your deposit request has been Completed :amount created by you or your team.',
    'created_amount' => 'Payout Successful :amount created by Admin Panel.',
    'created_subject' => 'Your payout has been Created by Admin Panel.',
    'periodic_payout_subject' => 'Your :period payout request has been created.',
    'periodic_payout_amount' => 'We are currently reviewing your :period payout request :amount created by system.',
  ],
  // End Wallet

  // 5. Inspector
  'inspector' => [
    'filter' => 'Filter',
    'enter_your_keywords' => 'Enter all the restricted keywords that will be used by the plugin to filter contents. Separated by comma(,)',
    'inspecting_subject' => 'Prohibited content detected by our inspector; we are inspecting.',
    'approved_subject' => 'Congratulations! Your content is Approved.',
    'deny_subject' => 'Prohibited content detected by our inspector; your content is denied.',
    'greeting' => "Hello :receiver",
    'inspecting_content' => 'We are currently inspecting a :inspecting created by you or your team.',
    'inspecting_message' => 'Our system inspector detected content created by you or your team which includes prohibited word <strong>:caught</strong>. Here are the full list of prohibited words <i>[:keywords]</i>. Please clean your content before posting again.',
    'approved_content' => 'We\'ve approved a :approved created by you or your team.',
    'deny_content' => 'We\'ve deny a :deny created by you or your team. review your content and try again.',
    'update' => "Update Now",
    'see_now' => "See Now",
    'status' => "Status",
    'thanks' => 'Thank you',
    'approved' => 'Approved',
    'pending' => "Pending",
    'blocked' => "Blocked",
    'inspection' => 'Inspection',
    'inspector_settings' => 'Inspector settings',
    'inspector_keywords' => 'Inspectable keywords',
    'type' => 'Type',
    'caught' => 'Caught',
    'prohibited' => 'Prohibited Keyword',
    'attempt' => 'Attempts',
    'option' => 'Options',
    'inspectables' => 'Inspectables',
    'inspectable' => 'Inspectable',
    'shop' => 'Shop',
    'inspecting_keywords' => 'The system inspector detected prohibited word <strong>:caught</strong> in :model\' :field field.',
    'update_to_approve' => 'Please make changed to approve the data to be public. Updating without making any changes will approve the content that have prohibited keywords!',
    'approve_success' => 'The item approved successfully.',
    'approve_failed' => 'The item approve failed.',
    'deny_success' => 'The item denied successfully.',
    'inspectable_updated' => 'The inspectable data has been updated successfully',
  ],

  // 6. Local Subscription
  'subscription' => [
    'bill_amount' => 'Bill amount',
    'custom_subscription_fee' => 'Custom subscription fee',
    'custom_subscription_fee_amount' => 'Custom subscription fee :amount',
    'custom_subscription_fee_help_text' => 'If set, the system will ignore the subscription plans fee and this custom fee will be charged for this vendor.',
    'subscription_fee_for' => 'Subscription fee of :subscription plan(:from - :to).',
    'thanks' => 'Thanks',
    'button_text' => 'Visit Your Profile',
    'greeting' => 'Hello :shop',

    'expire' => [
      'subject' => 'Shop subscription expiring in :day days',
      'message' => 'Please keep sufficient amount on you store wallet balance to get uninterrupted service.',
      'expire_date' => 'Expire Date  :date',
    ],

    'billing_failed' => [
      'subject' => 'Subscription billing failed',
      'message' => 'Subscription billing failed due to insufficient balance. Please deposit balance into your wallet to renew your subscription',
      'expire_date' => 'Expire Date  :date',
    ],

    'insufficient_balance' => [
      'subject' => 'Insufficient Balance',
      'message' => 'Your current wallet balance is running low. Please deposit balance into your wallet to continue your subscription, your current balance is :amount',
      'expire_date' => 'Expire Date  :date',
    ]
  ],

  // 7. Checkout All
  'checkout' => [
    'checkout_all' => 'Checkout All',
    'onecheckout_not_available' => 'All checkout option is not available right now!',
    'help_checkout_all' => 'All orders need to have the same delivery location to checkout all',
    'no_delivery_address_on_zone' => 'No delivery address found for your selected shipping area. Please add new address or update carts.',
    'checkout_all_not_possible' => 'One or many orders can not be processed together! Please check delivery to locations and other information.',
    'make_changes_on_cart_page' => 'Make changes on the cart page.',
  ],

  // 11. Eventy
  'eventy' => [
    'active' => 'Active',
    'create_event' => 'Create event',
    'description' => 'Description',
    'ends' => 'Ends',
    'event' => 'Event',
    'events' => 'Events',
    'inactive' => 'Inactive',
    'map_link' => 'Map link',
    'private' => 'Private',
    'public' => 'Public',
    'schedule' => 'Schedule',
    'slug' => 'Slug',
    'starts' => 'Starts',
    'status' => 'Status',
    'title' => 'Title',
    'type' => 'Type',
    'venue' => 'Venue',
    'placeholder_venue' => 'Enter the venue details',
    'event_details' => 'Event details',
    'map_location' => 'Location on map',
    'share_with_friends' => 'Share With Friends',
    'date_time' => 'Date & Time',
    'location' => 'Location',
    'upcoming_events' => 'Upcoming events',
    'event_book_success' => 'This event has booked successfully',
    'registered_user' => 'Registered users of this event',
    'my_events' => 'My Events',
    'book_this_seat' => 'Book this seat',
    'cancel_this_event' => 'Cancel This Booking',
    'event_cancel_success' => 'This event has canceled successfully',
    'registered' => 'Registered',
    'event_booked' => 'Event is booked successfully',
    'event_canceled' => 'Event is canceled successfully',
  ],

  // 12. dynamic Commission
  'dynamicCommission' => [
    'add_more_milestone' => 'Add more milestone',
    'and_up' => 'and up',
    'commission' => 'Commission',
    'commission_rate' => 'Commission rate',
    'commissions_settings' => 'Commissions',
    'custom_commission_rate' => 'Custom commission rate',
    'custom_commission_percent' => 'Custom commission rate :percent%',
    'custom_commission_rate_help_text' => 'If set, the system will use this custom fee to charge marketplace commission for this vendor.',
    'dynamic_commissions' => 'Dynamic commissions',
    'how_it_works' => 'The dynamic commissions will apply based on the total amount sold by the vendor till now or in the current payout period(excluding the current order amount for which calculating the commission for). When the <strong>reset on payout</strong> option is enabled the total sold amount will be reset to zero and dynamic commissions will start calculating from the start.',
    'milestone_amount' => 'Milestone amount',
    'periodic_sold_amount' => 'Periodic sold amount',
    'sold_amount_reset_on_payout' => 'Reset on payout',
    'sold_amount_reset_on_payout_help_text' => 'If enabled. The sold amount of vendors will be reset to zero(0) when the payout executed. Ideal if you want to calculate the dynamic commissions for a payout period.',
    'update_success' => 'Dynamic commissions updated successfully. ',
    'when_sold' => 'When sold',
  ],

  // 15. Pharmacy
  'pharmacy' => [
    'pharmacy' => 'Pharmacy',
    'expiry_date' => 'Expiry date',
    'help_expiry_date' => 'The expiry date of the item. This is important for items like medicine and food',
    'prescription' => 'Prescription',
    'upload_prescription' => 'Upload prescription',
  ],

  // 18. Sign in with Apple
  'apple-login' => [
    'login_with_apple' => 'Sign in with Apple',
    'apple' => 'Apple',
  ],

  // 17. PayPal Marketplace
  'paypal-marketplace' => [
    'redirect_text' => 'Return to the partner site now.',
    'will_redirect_to_paypal_connect' => 'You need to connect a PayPal account to start accepting payments. Complete the onboarding process on PayPal\'s website. It’s free to connect, whether you have an existing PayPal account, or want to create a new account.',
    'connect_paypal_account' => 'Connect PayPal',
    'paypal_business_email' => 'PayPal business email',
  ],

  // 19. M-Pesa Payment (Mpesa)
  'mpesa' => [
    'confirm_order' => 'Confirm order',
    'bad_request' => 'Bad request',
    'error_response' => 'The M-PESA server returns error! Details can be found in log.',
    'mpesa_number' => 'MPESA Number',
    'pay_with_mpesa' => 'You will get the payment request on your phone, Enter your MPESA PIN on your phone to complete the payment.',
    'payment_confirmation' => 'After completing the payment on M-PESA, please click the <strong>Confirm Order</strong> button below to complete the order.',
    'lipa_na_mpesa' => 'Lipa Na M-Pesa',
    'mpesa_passkey' => 'M-Pesa Passkey',
    'short_code' => 'M-Pesa Short Code',
    'consumer_secret' => 'Consumer Secret',
    'consumer_key' => 'Consumer Key',
  ],

  // 21. FlutterWave Payment
  'flutterwave' => [
    'pay_with_flutterwave' => 'Securely pay with FlutterWave.',
    'flutterwave_pub_key' => 'FLUTTERWAVE Public Key',
    'flutterwave_sec_key' => 'flutterwave Secret Key',
    'config_error' => 'FAILED TO CONNECT WITH FLUTTERWAVE API',
  ],

  // 22. Ajax Search Autocomplete
  'searchAutocomplete' => [
    'search_settings' => 'Search settings',
    'min_char' => 'Minimum characters',
    'min_char_help' => 'The minimum characters need to type to get autocomplete results.',
    'show_char' => 'Maximum characters',
    'show_char_help' => 'The maximum number of characters will show from the title.',
    'max_result' => 'Maximum results',
    'max_result_help' => 'The maximum number items will show on result',
    'styling_css' => 'Styling CSS',
    'styling_css_help' => 'Custom CSS to change the look and feel of the autocomplete dropdown. No need to change if the current design is good for you.',
  ],

  // 23. Trending Search Keywords
  'trendingKeywords' => [
    'trending_keywords' => 'Trending keywords',
    'description' => 'Trending keywords will be shown under the main searchbox, this feature will help customers to faster search.',
  ],

  // 26.Google Analytics
  'analytics' => [
    'analytics' => 'Analytics',
    'visitors' => 'Visitors',
    'behavior' => 'Behavior',
    'most_visited_pages' => 'Most visited pages',
    'top_referrals' => 'Top referrals',
    // 'misconfigured_google_analytics' => 'The Google Analytics report is enabled but not configured or misconfigured! Please check the documentation for help.',
  ],

  // 28. Authorizenet
  'authorizenet' => [
    'config_authorize_net_transaction_key' => 'The transaction key from Authorize.net. If you\'re not sure, contact Authorize.net to get help.',
    'config_authorize_net_api_login_id' => 'The API login ID from Authorize.net. If you\'re not sure, contact Authorize.net to get help.',
    'authorize_net_api_login_id' => 'AuthorizeNet API login ID',
    'authorize_net_transaction_key' => 'AuthorizeNet transaction key',
  ],

  // 29. Live Chat
  'liveChat' => [
    'fb_page_id' => 'Facebook Page ID',
    'shop_fb_page_id' => 'Facebook page id from messenger chat plugin script of page. Keep this field blank for use system live chat',
  ],

  // 30. Announcement
  'announcement' => [
    'announcements' => 'Announcements',
    'add_announcement' => 'New announcement',
    'expire_at' => 'Expire at',
    'call_to_action' => 'Call to action',
    'created_by' => 'Created by',
    'announcement_action_text' => 'Optional action button to link the announcement to any url',
    'announcement_action_url' => 'The action url to blog post or any url',
    'announcement_expire_at' => 'The announcement will be hidden from the viewers at this timestamp.',
    'announcement_public' => 'The public announcements will be visible to the storefront and customers will see it.',
    'announcement_body' => 'This is an **example** announcement!',
  ],

  // 32. Packaging and Gift Wrap
  'packaging' => 'Packaging',
  'packagings' => 'Packagings',
  'add_packaging' => 'Add Packaging',
  'packaging_name' => 'Packaging name',
  'set_as_default_packaging' => 'Set as default packaging',
  'packaging_cost' => 'Packaging cost',
  'width' => 'Packaging width',
  'height' => 'Packaging height',
  'depth' => 'Packaging depth',
  'packaging_not_available' => 'Packaging not available for this item',
  'default_packagings' => 'Default packagings',

  // 36. Sign in with Google

  // 42. eBay Sync
  'welcome' => 'Welcome to the eBay sync plugin. Your marketplace is ready to connect with eBay store. After you authorize, the marketplace will be able to update your eBay store\'s data.',
  'ebay_connected' => 'Your marketplace is authorized and connected with eBay store! No need to do anything, you can refresh the authorization token if you want.',
  'token_valid_untill' => 'Your authorization token will expire at :time',
  'refresh_token' => 'Refresh token',
  'ebay_settings' => 'eBay Settings',
  'connect' => 'Connect with eBay',
  'auth_success' => 'The authorization has been done successfully! You marketplace is now ready to sync with your eBay store.',
  'auth_failed' => 'The authorization has been done failed! Check your configurations and settings.',
  'auth_declined' => 'The authorization was declined by the user!',
  'misconfigured' => 'The eBay plugin is not configured or misconfigured! Please check your settings on .env file and update EBAY_APP_ID, EBAY_DEV_ID, EBAY_SECRET, EBAY_RU_NAME and EBAY_SANDBOX value with your eBay store configuration values.',
  'misconfigured_utilities' => 'All required utilities like MARKETPLACE_ID, POLICIES, MERCHANT_LOCATION_NAME, CONTENT_LANGUAGE are not configured or misconfigured. Please configure before start using it.',
  'pull_orders' => 'Pull orders from eBay',
  'pulling_orders' => 'Pulling orders, it may take sometimes.',
  'order_pulling_complete' => 'Order pulling completed successfully!',
  'token_expired_or_not_set' => 'The user token or refresh token is not set or expired! Please re-authorize to get the token.',
  'get_utilities_for_config' => 'To configure your platform with eBay, you will need some IDs and values from your eBay store configurations. This page will help you to get those values. Before you can use this too store, you must have to link your platform to eBay. After that this tool can access the configuration of your eBay.',
  'invalid_utility_key' => 'Invalid utility key given. Please check your input.',
  'copy_the_value_you_need_to_configure' => 'Please copy the value you need and place it to the .env file.',
  'response_from_ebay' => 'Response from eBay',
  'back_to_setting' => 'Back to setting',
  'get_utilities' => 'Get utilities',
  'payment_policy' => 'Payment policy',
  'fulfillment_policy' => 'Fulfillment policy',
  'return_policy' => 'Return policy',
  'take_back_policy' => 'Take back policy',
  'locations' => 'Locations',
  'default_category_tree' => 'Default category tree',

  // 43. Product Comparison
  'product_comparison' => [
    'add_to_compare'      => 'Add to compare',
    'add_more_items'      => 'Add more items',
    'clear_all'           => 'Clear All',
    'product_comparison'  => 'Product comparison',
    'item_added' => 'Item added to compare list',
    'item_exist' => 'Item already exist in compare list',
    'item_removed'   => 'Item removed successfully!',
    'remove_from_comparison' => 'Remove this item from comparison.',
    'no_items_in_comparison' => 'No items to compare!',
    'filter_criteria' => 'Filter comparison criteria',
    'select_criteria'     => 'Select criteria',
    'select_criteria_info' => 'Choose criteria to filter table below.',
    'technical_specifications' => 'Technical specifications',
    'price_rating' => 'Price &amp; rating',
    'general' => 'General',
    'seller' => 'Seller',
    'rates' => 'Rates',
    'shipping_delivery' => 'Shipping &amp; delivery',
    'shipping_details' => 'Shipping &amp; Delivery Details',
    'need_two_items_min'  => 'At least two items are required to compare features. Please add more items.',
  ],

  // 45. Login With OTP
  'otp-login' => [
    'phone' => 'Phone',
    'valid_phone' => 'Enter a valid phone number',
    'not_registered' => 'Number not registered yet',
    'enter_your_number' => 'Enter Your Phone Number',
    'verify_your_number' => 'Verify Your Phone Number',
    'verify_number' => 'Verify Phone Number',
    'verification_code' => 'Verification Code',
    'enter_otp' => 'Please enter the OTP sent to your number:',
    'back' => 'BACK',
    'resend' => 'RESEND',
    'phone_session_expired' => 'Your session has been expired! Please enter the phone number again.',
    'enter_verification_code' => 'Enter your verification code',
    'invalid_otp' => 'Invalid verification code entered!',
    'wrong_credential' => 'Check your twilio credential maybe it\'s wrong ',
    'login_with_phone' => 'Login With Phone',
    'login_with_email' => 'Login With Email',
    'verification_code_sent' => 'Verification code has been sent successfully',
  ],
  // 52. bKash Payment
  'bkash' => [
    'pay_with_bkash' => 'Use bKash to pay your bills',
    'bkash' => 'bKash payment',
    'bkash_username' => 'Bkash API user name',
    'bkash_password' => 'Bkash API password',
    'bkash_app_key' => 'BKash API app key',
    'bkash_app_secret' => 'Bkash API app secret',
    'enter_bkash_number' => 'Enter your bKash number',
    'bkash_locality' => 'Bkash only works when system currency is in with Bangladeshi taka(BDT).',
  ],

  // 54. Affiliate
  'affiliate' => [
    'affiliate' => 'Affiliate',
    'affiliates' => 'Affiliates',
    'affiliate_dashboard' => 'Affiliate Dashboard',
    'create_affiliate' => 'Create Affiliate',
    'edit_affiliate' => 'Edit Affiliate',
    'delete_affiliate' => 'Delete Affiliate',
    'username' => 'Affiliate Username',
    'username_is_not_available' => '* This Username is not available',
    'username_is_available' => '* This Username is available',
    'placeholder_username' => 'Enter unique username',
    'affiliate_created' => 'Affiliate created successfully.',
    'affiliate_updated' => 'Affiliate updated successfully.',
    'affiliate_deleted' => 'Affiliate deleted successfully.',
    'affiliate_register' => 'Register',
    'affiliate_marketing' => 'Affiliate Marketing',
    'notification_password_updated' => 'Password updated successfully',
    'visitors_brought' => 'Visitors Brought',
    'visitors_by_link' => 'Visitors by Link',
    'products_sold' => 'Products Sold',
    'commission' => 'Commission',
    'commission_to' => 'Commission To',
    'commission_from' => 'Commission From',
    'commission_for_order' => 'Commission for Order :order',
    'commission_by_link' => 'Commission by Link',
    'commission_by_shop' => 'Commission by Shop',
    'daily_commissions' => 'Daily Commissions',
    'default_affiliate_commission_percentage' => 'Default Affiliate Commission',
    'placeholder_default_affiliate_commission_percentage' => 'Set your shop\'s default affiliate commission in percentage value.',
    'help_default_affiliate_commission_percentage' => 'This commission percentage will be used for all products if the product commission is not set. Input must be between (0-100)%',
    'help_commission_field' => 'Set 0 to disable commissions for this item.',
    'placeholder_commission_field' => 'Enter the commission amount in percentage',
    'affiliate_commission' => 'Affiliate Commission',
    'affiliate_links' => 'Affiliate Links',
    'top_links_by_visitors' => 'Top Links by Visitors',
    'top_links_by_commission' => 'Top Links by Commission',
    'toggle_navigation' => 'Toggle Navigation',
    'invalid_links' => 'Invalid links',
    'create_affiliate_link' => 'Create Affiliate Link',
    'create_your_link' => 'Create Your Link',
    'update_affiliate_link' => 'Update Affiliate Link',
    'edit_affiliate_link' => 'Edit Affiliate Link',
    'in_your_portfolio' => 'In Your Portfolio',
    'go_to_product_page' => 'Go to product page',
    'start_earning' => 'Start Earning',
    'login' => 'Affiliate Login',
    'become_an_affiliate' => 'Become an Affiliate',
    'affiliate_commission_release_in_days' => 'Affiliate commission release in',
    'link_created_successfully' => 'Affiliate link has been created successfully.',
    'link_updated_successfully' => 'Affiliate link has been updated successfully.',
    'link_deleted_successfully' => 'Affiliate link has been deleted.',
    'item_not_available' => 'Item not available for sale',
    'affiliate_commission_release_in_days_help' => 'Specify the number of days after an order is placed and paid for when the affiliate commission will be released. Set 0 to release the commission immediately when an order has been paid. Keep empty/null to release manually by admins.',
    'slug_edit_warning' => 'Updating the slug will invalidate the previous link. This is not recommended if you have already shared the link with others.',
    'publicly_show_affiliate_commission' => 'Publicly show affiliate commission',
    'help_publicly_show_affiliate_commission' => 'Do you want to display the affiliate commission publicly on the product page? Turn off this feature to make it visible only to registered affiliate users.Displaying the affiliate commission publicly may attract more affiliate partners.',
    'pending_commission' => 'Pending Commission',
    'last_commission' => 'Last Commission',
    'affiliate_commissions' => 'Affiliate Commissions',
    'created_at' => 'Created At',
    'amount' => 'Amount',
    'status' => 'Status',
    'option' => 'Option',
    'release' => 'Release',
    'released' => 'Released',
    'order' => 'Order',
    'pending' => 'Pending',
    'received' => 'Received',
    'item' => 'Item',
    'quantity' => 'Quantity',
    'commission_is_released' => 'Commission has been released successfully',
    'affiliate_log_in_successful' => 'Affiliate log in successful',
    'successfully_registered_as_affiliate' => 'You have successfully registered as an affiliate',
    'order_has_no_affiliate_commission' => 'Order has no affiliate Commission',
    'you_dont_have_any_links_yet' => 'You don\'t have any affiliate link yet!',
    'you_dont_have_any_links_with_commission' => 'You don\'t have any affiliate link with commission yet!',
    'when_empty_commission_will_calculated_from_default' => 'If empty, default affiliate commission percentage will be used.',
    'slug' => 'Slug',
    'visitors' => 'Visitors',
    'commission_rate' => 'Commission rate',
    'toggle_navigation' => 'Toggle navigation',
  ],

  // 55. Dynamic Currency
  'dynamic-currency' => [
    'currency_api' => 'Currency API',
    'currency_live_mode' => 'Currency live mode',
    'currency_mode' => 'Currency live mode',
    'currency_api_key' => 'Currency API key',
    'exchange_rate' => 'Exchange rate',
    'currency_mode_help' => 'Keep empty to use manual rates, set -1 for real time live updates, and set any specific value from 00 to 23 to update at that specific time of the day.',
    'currency_exchange_rate' => 'The exchange rate of the currency. Default value is 1.',
    'currency_api_key_help' => 'Currency API Key from website',
    'currency_changed_successfully' => 'Currency has been changed successfully',
  ],

  // 56. POS
  'pos' => [
    'pos' => 'POS',
    'cart' => 'Cart',
    'cashier' => 'Cashier',
    'product_name' => 'Product Name',
    'quantity' => 'Quantity',
    'total_items' => 'Total Items',
    'total_quantities' => 'Total Quantities',
    'qtt' => 'Qtt',
    'sku' => 'SKU',
    'search' => 'Search',
    'price' => 'Price',
    'unit_price' => 'Unit Price',
    'total_price' => 'Total Price',
    'total' => 'Total',
    'sub_total' => 'Sub Total',
    'grand_total' => 'Grand Total',
    'discount' => 'Discount',
    'subtotal' => 'Subtotal',
    'payment' => 'Payment',
    'paid' => 'Paid',
    'due' => 'Due',
    'change' => 'Change',
    'cash' => 'Cash',
    'cheque' => 'Cheque',
    'gift_card' => 'Gift Card',
    'other' => 'Other',
    'total_payable' => 'Total Payable',
    'money_received' => 'Money Received',
    'money_returned' => 'Money Returned',
    'money_due' => 'Due',
    'money_return' => 'Return',
    'change' => 'Change',
    'refund' => 'Refund',
    'payment_method' => 'Payment Method',
    'placeholder_search_products' => 'Search Products',
    'order' => 'Order',
    'checkout' => 'Checkout',
    'sales_point' => 'Sales Point',
    'add_to_order' => 'Add to Order',
    'brand' => 'Brand',
    'product_type' => 'Product Type',
    'receipt' => 'Receipt',
    'option' => 'Option',
    'no_items_in_cart' => 'Add item to the cart',
    'configuration' => 'Configuration',
    'products' => 'Products',
    'template_name' => 'Template Name',
    'paper_size' => 'Paper Size',
    'orientation' => 'Orientation',
    'is_download' => 'Auto Download',
    'portrait' => 'Portrait',
    'landscape' => 'Landscape',
    'update_config' => 'Update Configuration',
    'height' => 'Height',
    'width' => 'Width',
    'scale' => 'Scale',
    'checkout_note' => 'Checkout Note',
    'invalid_qtt' => 'Invalid cart or quantity',
    'cart_no_found' => 'The cart is not found, created a new card for you.',
    'cant_checkout_empty_cart' => 'Cant checkout empty cart',
    'excluded_product_types' => 'Digital and Auctionable items are excluded automatically.',
    'order_created' => 'The order has been created successfully',
    'cancel_order' => 'Cancel order',
    'print_order' => 'Print order',
    'print_bill' => 'Print bill',
    'back_to_pos' => 'Back to POS',
    'sales_person' => 'Sales Person',
    // 'walk_in_customer ' => 'Walk-in Customer',
  ],

  // 57. Dynamic Popup
  'dynamic_popups' => 'Dynamic Popups',
  'popup_type' => 'Popup Type',
  'popup_delay_time' => 'Popup Delay Time',
  "newsletter" => 'Newsletter',
  "banner" => 'Banner',
  "none" => 'None',
  'popup_img_max_size' => 'File size can not exceed :size KB',
  'custom_css' => 'Custom CSS',
  'custom_css_help' => 'Add your custom styling CSS in the box. This will override the theme design.',
  'popup_delay_time_help' => 'Set the time in millisecond you would like to delay to show popup after the site load',
  'updated' => 'Popup has been updated successfully',
  'subscribe_newsletter' => 'Subscribe to our newsletter',
  'newsletter_description' => 'Signup for our weekly newsletter to get the latest news, updates and amazing offers delivered directly in your inbox.',
  'subscribe' => 'Subscribe',

  // 62. shopify
  'shopify' => [
    'shopify' => 'Shopify',
    // 'welcome_message' => 'Welcome to Shopify Import',
    'configure_api' => 'Configure API',
    'update_api' => 'Update API',
    'import_inventory' => 'Import inventory from Shopify',
    'shopify_imports' => 'Shopify imports',
    'api_credentials_are_missing' => 'API credentials are missing',
    'shopify_api_access_token' => 'Shopify API Access Token',
    'shopify_shop_domain' => 'Shopify Shop Domain',
    'placeholder_shopify_shop_domain' => 'Enter Shopify Shop Domain',
    'placeholder_shopify_api_access_token' => 'Enter Shopify API Access Token',
    'imported_from_shopify' => 'This has been imported from shopify',
    'welcome_message_for_no_credential_details' => 'You have not entered your Shopify API credentials yet. First enter your API Credentials to get started.',
    'welcome_message_with_credentials_details' => "Your shopify store has :shopify_products_count products. You currently have :merchant_products_count products imported from shopify.",
    'shopify_import_history' => 'Shopify import history',
    'import_history_deleted' => 'Import history has been deleted',
    'download_csv' => 'Download CSV',
    'no_imports_made_yet' => 'No imports made yet',
    // 'import_status' => 'Shopify import :status.',
    'import_started' => 'Shopify import has been started, will be process as background task.',
    'import_completed_email_subject' => 'Your Shopify import has been completed',
    // 'shopify_import_completed_email_subject'
    'completed' => 'Completed',
    'failed' => 'Failed',
    'started' => 'Started',
    'imported' => 'Imported',
    'graphql_query_error' => 'GraphQL query error: :error',
    'help_shopify_api_access_token' => 'Enter your Shopify API access token with read_products permission.',
    'help_shopify_shop_domain' => 'Enter your Shopify shop domain name. Example: exampleshop.myshopify.com',
    'warning_shopify_api_access_token' => 'API access token must have read_products permission.',
    'warning_list_title' => 'Please read the following details carefully before importing your shopify products.',
    'warning_currency_requirements' => 'We don\'t convert currency. Make sure your Shopify currency setting is the same as ours.',
    'warning_list_requirements' => 'For your shopify products to be imported, your shopify products will require <b>:required_fields</b>. Products missing these fields will be skipped.',
    'warning_list_only_images_will_be_imported' => 'Only images will be imported from shopify products media.',
    'warning_list_notifications_for_import' => "Imports run in the background. You'll receive email and in-app notifications upon completion, including a CSV of any failed/skipped imports.",
    'import_completed_greeting' => 'Hello,',
    'import_completed_content' => 'The import of your Shopify products to our platform has been completed. Please click the button below to review the import details',
    'thank_you' => 'Thank you for using our platform',
    'view_details' => 'View details'
  ],

  // 76. Shippo
  'shippo' => [
    'shippo' => 'Shippo',
    'fetch_carriers_btn_label' => 'Fetch Shippo Carriers',
    'shippo_merchant_key' => 'Enter your Shippo API key',
    'configurations' => 'Configurations',
  ],

  // 77. Auction
  'auction' => [
    'auction' => 'Auction',
    'auctions' => 'Auctions',
    'base_price' => 'Base Price',
    'auction_status' => 'Auction status',
    'auction_start' => 'Auction Start',
    'auction_end' => 'Auction End',
    'auction_running' => 'Running',
    'auction_paused' => 'Paused',
    'auction_ended' => 'Ended',
    'auction_ended_at' => 'Auction ended at',
    'auction_suspended' => 'Suspended',
    'auction_schedule' => 'Auction schedule',
    'auction_start_help' => 'Starting time of the bidding',
    'auction_end_help' => 'End time of the bidding',
    'auction_items' => 'Auction Items',
    'auction_end_in' => 'Ending in',
    'auction_will_end_in' => 'Auction ending in',
    'item_not_auctionable' => 'This item is not auctionable.',
    'days' => 'days',
    'hrs' => 'hrs',
    'mins' => 'mins',
    'sec' => 'sec',
    'place_bid' => 'Place Your Bid',
    'bid_amount' => 'Bid amount',
    'current_bid' => 'Current bid',
    'base_price_help' => 'The bid amount will be equal to or greater than the base price.',
    'bid' => 'Bid',
    'bids' => 'Bids',
    'your_bid_was' => 'Your bid was',
    'your_bid' => 'Your bid',
    'accept' => 'Accept',
    'accepted' => 'Bid has been accepted',
    'top_bider' => 'Top bider',
    'auction_ended_ago' => 'Auction ended',
    'auction_continue' => 'Auction continue',
    'bid_accept_action' => 'Bid accept action',
    'bid_placed_successfully' => 'Bid placed successfully!',
    'your_bid_should_be_greater' => 'The bid should be greater than :amount',
    'bid_wins_at' => 'Bid wins at',
    'bid_agreement' => 'By placing a bid, you are agreeing to buy the item if you win the auction.',
    'thank_you_for_bid' => 'Thank you for your bid.',
    'winner' => 'Winner!',
    'winner_msg' => 'You\'re the winner of an auction. Please proceed the checkout to complete the purchase.',
    'complete_purchase' => 'Complete The Purchase',
    'cart_warning' => 'As per the bidding agreement you\'re committed to buying this item as the winner and you can not remove this item from the cart.',
    'winner_mail_subject' => 'You\'re the winner of an auction. Please complete your order.',
    'auction_end_validation' => 'The auction end must be a date after available from.',
    'help_bid_accept_action' => 'Will the auction go live again or end the auction when you accept a bid? When you choose to continue, the system will set an ending date based on your previous setting.',
  ],

  // 80. SmartForm
  'smartForm' => [
    'smart_form' => 'Smart Form',
    'create_form' => 'Create Form',
    'smart_forms' => 'Smart Forms',
    'create_smart_form' => 'Create Smart Form',
    'edit_smart_form' => 'Edit Smart Form',
    'application_details' => 'Application Details',
    'additional_info' => 'Additional Information',
    'input_field' => 'Input Field',
    'input_fields' => 'Input Fields',
    'label_visibility' => 'Use to toggle label visibility',
    'add_option' => 'Add Option',
    'another_option' => 'Another option',
    'placeholder_text' => 'Enter the placeholder text',
    'form_label' => 'Label',
    'form_placeholder' => 'Placeholder',
    'form_required' => 'Required',
    'form_type' => 'Input Type',
    'form_requirement_type' => 'Is Required?',
    'form_default_value' => 'Default Value',
    'form_accepted_file_types' => 'Accepted File Types',
    'form_all_types_are_accepted' => 'All types are accepted',
    'form_file_type' => 'File Type',
    'form_enable_html' => 'Enable HTML',
    'additional_message_fields' => 'Additional Message Fields',
    'smart_form_for_contact_us_page' => 'Smart Form for Contact Us Page',

    'notify_smart_form_updated_successfully' => 'Smart Form updated successfully',
    'notify_smart_form_created_successfully' => 'Smart Form created successfully',
    'notify_failed_to_update_smart_form' => 'Failed to update Smart Form',

    'placeholder_smart_form_name' => 'Enter Smart Form Name',
    'placeholder_smart_form_description' => 'Enter Smart Form Description',
    'placeholder_file_types' => 'Example : jpg, jpeg, png, pdf',
    'placeholder_enter_label' => 'Enter the input field label',

    'help_smart_form_name' => 'Name of the smart form',
    'help_smart_form_description' => 'Give a short description for better understanding about the form.',
    'help_add_fields' => 'Add input fields to the form',
    'help_requirement_type' => 'Is the input field required? The user must fill in the required fields before submitting the form.',
    'help_default_value' => 'Enter the default value',
    'help_accepted_file_types' => 'Insert accepted filetypes as comma separated string',
    'help_input_type' => 'The type of input field that will appear in registration form for customers.',
    'help_label' => 'The title or label of the input field to be appeared on the input field.',
    'help_select_buyer_grp_form' => 'This form will be added to the customer registration form to get additional data your business may needed.',
    'help_smart_form_for_contact_us_page' => 'Add the additional input fields in the form to the contact us page',
    'help_enable_html' => 'Enable HTML tags in the input field label and options (for radio, checkbox, dropdown)',

    'messages_input_field_required' => 'You can not create smart form without any input field, At least one input field is required.',
    'messages_input_field_label_required' => 'Missing label for input field.',
    'messages_input_field_label_each_required' => 'Every input field must have a label. Label can\'t be empty',
    'messages_input_field_label_each_string' => 'Labels for input fields have to be a string.',
    'messages_input_field_placeholder_required' => 'The placeholder field under input field is required.',
    'messages_input_field_placeholder_array' => 'The placeholder field under input field must be an array.',
    'messages_input_field_type_required' => 'The type field under input field is required.',
    'messages_input_field_type_each_required' => 'Each input field must have a type.',
    'messages_input_field_requirement_type_required' => 'The requirement field under input field is required.',
    'messages_input_field_requirement_type_each_required' => 'You must specify whether an input field is required or not for each input field.',
    'messages_name_required' => 'The name field can\'t be empty, please enter name for smart form.',
    'messages_name_is_string' => 'The name field must be a string.',
    'messages_description_required' => 'The description field cant be empty, please add a description.',
    'form_data_requied_validation_msg' => 'Please submit all required data.'
  ],

  // 82. Wholesale
  'wholesale' => [
    'wholesale_prices' => 'Wholesale Prices',
    'min_quantity' => 'Min Quantity',
    'wholesale' => 'Wholesale',
    'add_another_pricing' => 'Add another pricing',
    'minimum_wholesale_quantity' => 'Minimum quantity to buy for specific wholesale price to be applicable',
    // 'wholesale_price_for_quantity' => 'Wholesale price for the given quantity',
  ],

  // upiPayment
  'upiPayment' => [
    'api_key' => 'API Key',
    'payment_address' => 'Payment Address',
    'payment_name' => 'Payment Name',
    'merchant_category_code' => 'Merchant Category Code',
    'currency_code' => 'Currency Code',
    'business_gstin' => 'Business GSTIN',
    'pay_with_upiPayment' => 'Pay with UPI',
    'enter_upiPayment_number' => 'Enter UPI Payment Number',
  ],

  // 85. buyerGroup
  'buyer_group' => 'Buyer Group',
  'buyer_groups' => 'Buyer Groups',
  'buyer_group_name' => 'Buyer Group Name',
  'create_buyer_group' => 'Create Buyer Group',
  'edit_buyer_group' => 'Edit Buyer Group',
  'select_buyer_group' => 'Select Buyer Group',
  'application_details' => 'Application Details',
  'add_buyer_group' => 'Add Buyer Group',
  'buyer_group_applications' => 'Buyer Group Applications',
  'requested_buyer_group' => 'Requested Buyer Group',
  'request_buyer_group' => 'Request Buyer Group',
  'application_status' => 'Application Status',
  'current_buyer_group' => 'Current Buyer Group',
  'no_buyer_group' => 'None',
  'applications' => 'Applications',
  'apply_for_buyer_group' => 'Apply for Buyer Group',
  'pending_applications' => 'Pending Applications',
  'change_buyer_group' => 'Change Buyer Group',
  'download_report' => 'Download Report',
  'cost_distribution' => 'Cost Distribution',
  'total_ordered_by_shop' => 'Total Ordered By Shop',
  'number_of_users' => 'Members',
  'view_customers' => 'View customers',
  'buyer_group_required' => 'You must select a buyer group to register',
  'all_types_are_accepted' => 'All types are accepted',
  'file_type' => 'File Type',
  'report_type' => 'Report Type',
  'order_report' => 'Order Report',
  'discount_report' => 'Discount Report',

  'buyer_group_application_changed_successfully' => 'Buyer Group application status changed successfully',
  'buyer_group_updated_successfully' => 'Buyer Group updated successfully',
  'buyer_group_deleted_successfully' => 'Buyer Group deleted successfully',
  'buyer_group_created_successfully' => 'Buyer Group created successfully',
  'failed_to_create_buyer_group' => 'Failed to create Buyer Group',
  'failed_to_update_buyer_group' => 'Failed to update Buyer Group',
  'failed_to_delete_buyer_group' => 'Failed to delete Buyer Group',
  'failed_to_change_buyer_group' => 'Failed to change Buyer Group',
  'buyer_group_application_status_updated' => 'Buyer Group application status updated',
  'buyer_group_application_status_not_updated' => 'Buyer Group application status not updated',
  'must_select_buyer_group_to_apply' => 'You must select a buyer group to apply',

  'placeholder_buyer_group_name' => 'Enter Buyer Group Name',
  'placeholder_buyer_group_description' => 'Enter Buyer Group Description',
  'placeholder_application_details' => 'Enter Application Details',
  'placeholder_min_order_quantity' => 'Enter Minimum Order Quantity',
  'placeholder_buyer_group_price' => 'Enter Buyer Group Specific Price',

  'help_buyer_group_name' => 'Name of the buyer group',
  'help_buyer_group_description' => 'Description of the buyer group',
  'help_see_application_details' => 'See submitted application details of customer',
  'help_buyer_group_price' => 'Price for the buyer group. Default price will be used for the buyer group when left empty',
  'help_min_order_quantity' => 'Minimum order quantity for the buyer group. Default minimum order quantity will be used for the buyer group when left empty',
  'help_buyer_group_status' => 'Choose the status of the group.',
  'help_control_on_buyer_grp' => 'Control on Buyer Group settings',

  // 87. Ai Assistant
  'aiAssistant' => [
    'aiAssistant' => 'AI Assistant',
    'ai_assistant' => 'AI Assistant',
    'use_as_description' => 'Use as Description',
    'ai_generated' => 'AI Generated',
    'generate_description' => 'Generate Description',
    'original' => 'Original',
    'config' => 'Ai Assistant Configuration',
    'model' => 'Model',
    'chatgpt' => 'Chat GPT',
    'bard' => 'Bard',
    'api_key' => 'API Key',
    'help_api_key' => 'Enter your API key to for selected AI model.',
    'enter_description_first' => 'Please enter a description first.',
    'help_ai_assistant_generate' => 'Improve your product description with the power of AI. You need to enter basic details of your product in description first.',
    'description_generation_error_occurred' => 'An error occurred during description generation.',
    'prompt' => 'Prompt',
    'help_prompt' => 'The prompt used to generate a description for the product/inventory.',
    'restore_default' => 'Restore default',
    'default_prompt' => 'Generate a description for this product like an experienced copywriter. Give a long description.',
    'loading_ai_generated_description' => 'Loading AI Generated Description...',
  ],

  // 88. smsGateways
  'smsGateways' => [
    'smsGateways' => 'SMS Gateways',
    'welcome' => 'Welcome to the SMS Gateways plugin. Build something beautiful using this!',
    'bulk_sms' => 'Bulk SMS',
    'bulk_sms_description' => 'Send a sms to all registered customers or to a group of phone numbers through csv. Perfect for marketing campaigns.',
    'send_sms' => 'Send SMS',
    'configuration' => 'Configuration',
    'sms_history' => 'SMS History',
    'twilio' => 'Twilio',
    'vonage' => 'Vonage',
    'sid' => 'SID',
    'auth_token' => 'Auth Token',
    'verify_sid' => 'Verify SID',
    'from_number' => 'From Number',
    'message' => 'Message',
    'message_placeholder' => 'Type your message here',
    'options' => 'Options',
    'job_id' => 'Job ID',
    'upload_csv' => 'Upload CSV',
    'all_customers' => 'All Customers',
    'custom_list' => 'Custom List',
    'download_report' => 'Download Report',
    'bulk_sms_history' => 'Bulk SMS History',
    'api_key' => 'API Key',
    'api_secret' => 'API Secret',
  ],

  // 50. twoCheckout (2Checkout)
  'twoCheckout' => [
    'twoCheckout'   => 'TwoCheckout',
    'merchant_code' => 'Merchant Code',
    'secret_key'    => 'Secret Key',
    'secret_word'   => 'Secret Word',
    'sandbox'       => 'Sandbox',
    'pay_with_twoCheckout' => 'Pay with 2Checkout',
  ],

  // 70. mtnMoney
  'mtnMoney' => [
    'mtnMoney' => 'MTN Money',
    'api_key' => 'API Key',
    'api_secret' => 'API Secret',
    'environment' => 'Environment',
    'callback_url' => 'Callback URL',
    'callback_url_placeholder' => 'Enter your MTN Money callback URL',
    'pay_with_mtnMoney' => 'Pay with MTN Money',
    'config_updated' => 'MTN Money configuration has been updated successfully.',
  ],
];
