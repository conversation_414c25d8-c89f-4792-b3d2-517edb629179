<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Server Responses Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines are used for various
    | server responses that we need to display to the user. You are free to modify
    | these language lines according to your application's requirements.
    |
    */

    'success' => 'Action has been done successfully!',
    'failed' => 'Action failed! Something went wrong!!',
    'select_some_item' => 'Select at least one item',
    'database_connection_failed' => 'Can not connect to MySQL database. Check database connection details in the .env file.',
    'denied' => 'Permission denied!',
    '404' => 'This content is not available or you don\'t have permission to access this area',
    'error' => 'Action failed! There is something wrong on server side',
    'timeout' => 'Action failed! The request timeout',
    'reordered' => 'Successfully reordered',
    'no_files_to_upload' => 'No files found for upload.',
    'form_validation_failed' => 'Failed: Please check all required inputs are filled and valid',
    'vendor_config_failed' => 'Registration failed! Please make some changes and try again.',
    'no_product_found_for_inventory' => 'No result found! Please try different search key',
    'searching' => 'Searching...',
    'you_are_blocked' => 'You\'re temporarily blocked from this site!',
    '404_not_found' => 'Sorry, the page you are looking for could not be found.',

    // Version 1.3.3
    'no_file_was_uploaded' => 'No file was uploaded.',
    'error_uploading_file' => 'Error uploading file',
    'model_not_defined' => 'The model is not defined!',

    // Version 1.4.0
    'invalid_data' => 'Invalid data!',

    // Version 2.0.3
    'resource_not_found' => 'Resource not found.',
    'unauthenticated' => 'Unauthenticated.',
    'unauthorized' => 'Unauthorized',

    'subscription_404' => 'The customer does not have any subscriptions.',
    'otp_sent_successfully' => 'OTP sent successfully',
    'order_assigned_successfully' => 'Order assigned successfully',
];
